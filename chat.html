<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent <PERSON><PERSON><PERSON> Chat - HustlePlug</title>
  <link rel="stylesheet" href="styles/styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
  <div class="chat-container">
    <!-- Window Controls Bar -->
    <div class="window-controls">
      <div class="window-controls-left">
        <div class="window-dot red"></div>
        <div class="window-dot yellow"></div>
        <div class="window-dot green"></div>
      </div>
    </div>

    <!-- Chat Layout -->
    <div class="chat-layout">
      <!-- Sidebar -->
      <div class="chat-sidebar">
        <!-- New Chat Button -->
        <div class="sidebar-header">
          <button id="new-chat-btn" class="new-chat-btn">
            <svg class="new-chat-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 4.5v15m7.5-7.5h-15" stroke="currentColor" stroke-width="2"/>
            </svg>
            New chat
          </button>
        </div>

        <!-- Recent Conversations -->
        <div class="conversations-section">
          <div class="conversations-header">
            <h3 class="conversations-title">Today</h3>
            <div id="conversation-list" class="conversations-list">
              <button class="conversation-item active">
                <div class="conversation-text">
                  Explaining quantum computing
                </div>
              </button>
              <button class="conversation-item">
                <div class="conversation-text">
                  Creative writing prompts
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- User Profile -->
        <div class="sidebar-footer">
          <button class="profile-btn upgrade-btn">
            <svg class="profile-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Zm0 0a9 9 0 0 0 5.636-1.968m-11.272 0A9 9 0 0 0 12 21Z" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="9" r="3" stroke="currentColor" stroke-width="2"/>
            </svg>
            Upgrade to Pro
          </button>
          <button class="profile-btn user-btn">
            <div class="user-info">
              <div class="user-avatar">
                <span class="user-initials">HP</span>
              </div>
              <span class="user-name">HustlePlug User</span>
            </div>
            <svg class="menu-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 12h.01M12 6h.01M12 18h.01" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Main Chat Area -->
      <div class="chat-main">
        <!-- Chat Header -->
        <div class="chat-header">
          <div class="header-left">
            <div class="logo">
              <div class="logo-text">
                <h1>Agent Hustle</h1>
                <span class="pro-badge">PRO</span>
              </div>
            </div>
          </div>
          <div class="header-right">
            <button id="minimizeChat" class="window-control-btn">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 12h12" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
            <button id="closeChatWindow" class="window-control-btn close-btn">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Chat Messages -->
        <div id="chatMessages" class="chat-messages">
          <!-- Welcome message will be added by JavaScript -->
        </div>

        <!-- Chat Input -->
        <div class="chat-input-container">
          <div class="chat-input-wrapper">
            <textarea id="chatInput" placeholder="Message Agent Hustle..."></textarea>
            <button id="sendMessage" class="send-btn">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 12L3.269 3.126A59.768 59.768 0 0721.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
          <div class="chat-input-footer">
            <div id="chatStatus" class="chat-status">
              <span class="status-indicator connected"></span>
              <span class="status-text">Ready</span>
            </div>
            <div class="character-count">0/2000</div>
          </div>
          <div class="chat-disclaimer">
            Agent Hustle can make mistakes. Consider checking important information.
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="loading-indicator">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <span>Processing...</span>
      </div>
    </div>
  </div>

  <script src="chat.js"></script>
</body>
</html>
