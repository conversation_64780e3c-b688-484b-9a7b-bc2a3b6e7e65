/* Agent <PERSON><PERSON>le Chat Window Styles - ChatGPT Style Layout */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #141021;
    color: #F9F9F9;
    height: 100vh;
    overflow: hidden;
    user-select: none;
    padding: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Chat Container - ChatGPT Style */
.chat-container {
    width: 100%;
    max-width: 1200px;
    height: 700px;
    background: #141021;
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    border: 1px solid #2C2738;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Window Controls - macOS Style */
.window-controls {
    height: 24px;
    background: #1D1A2A;
    display: flex;
    align-items: center;
    padding: 0 12px;
}

.window-controls-left {
    display: flex;
    align-items: center;
    gap: 6px;
}

.window-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.window-dot.red {
    background: #FF5C5C;
}

.window-dot.yellow {
    background: #FFD84D;
}

.window-dot.green {
    background: #4CAF50;
}

/* Chat Layout - Sidebar + Main */
.chat-layout {
    display: flex;
    height: calc(100% - 24px);
}

/* Sidebar Styles - ChatGPT Style */
.chat-sidebar {
    width: 256px;
    background: #1D1A2A;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #2C2738;
}

.sidebar-header {
    padding: 16px;
}

.new-chat-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    border: 1px solid #2C2738;
    padding: 12px;
    font-size: 14px;
    font-weight: 500;
    background: transparent;
    color: #F9F9F9;
    cursor: pointer;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    background: #2C2738;
}

.new-chat-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.conversations-section {
    flex: 1;
    overflow-y: auto;
}

.conversations-header {
    padding: 0 12px 8px;
}

.conversations-title {
    font-size: 12px;
    color: #B2AFC5;
    font-weight: 500;
    margin-bottom: 8px;
    padding: 0 12px;
}

.conversations-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.conversation-item {
    width: 100%;
    text-align: left;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    background: transparent;
    color: #B2AFC5;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.conversation-item:hover {
    background: #2C2738;
}

.conversation-item.active {
    color: #F9F9F9;
}

.conversation-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sidebar-footer {
    border-top: 1px solid #2C2738;
    padding: 8px 16px 16px;
}

.profile-btn {
    width: 100%;
    text-align: left;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    background: transparent;
    color: #B2AFC5;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.profile-btn:hover {
    background: #2C2738;
}

.profile-btn.user-btn {
    color: #F9F9F9;
    justify-content: space-between;
}

.profile-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.user-initials {
    font-size: 12px;
    font-weight: 600;
    color: #F9F9F9;
}

.user-name {
    font-size: 14px;
}

.menu-icon {
    width: 16px;
    height: 16px;
}

/* Main Chat Area */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* Header Styles */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #1D1A2A;
    border-bottom: 1px solid #2C2738;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-right {
    display: flex;
    gap: 8px;
}

/* Logo Styles */
.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.logo-text h1 {
    font-size: 18px;
    font-weight: 700;
    color: #F9F9F9;
    line-height: 1.2;
}

.pro-badge {
    background: linear-gradient(135deg, #FFD84D, #F4BD61);
    color: #2A2C3D;
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: fit-content;
}

/* Window Controls */
.window-control-btn {
    width: 24px;
    height: 24px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: #B2AFC5;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.window-control-btn svg {
    width: 20px;
    height: 20px;
}

.window-control-btn:hover {
    background: #2C2738;
    color: #F9F9F9;
}

.window-control-btn.close-btn:hover {
    background: #FF5C5C;
    color: #F9F9F9;
}

/* Chat Messages Area */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #141021;
    scroll-behavior: smooth;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* Message Styling - ChatGPT Style */
.message-container {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-avatar {
    background: #5BA9F9;
}

.ai-avatar {
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
}

.system-avatar {
    background: #6B7280;
}

.avatar-text {
    font-size: 12px;
    font-weight: 600;
    color: #F9F9F9;
}

.avatar-icon {
    width: 20px;
    height: 20px;
    color: #F9F9F9;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.sender-name {
    font-size: 14px;
    font-weight: 600;
    color: #F9F9F9;
}

.message-time {
    font-size: 12px;
    color: #B2AFC5;
}

.message-text {
    font-size: 14px;
    line-height: 1.5;
    color: #F9F9F9;
    word-wrap: break-word;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #2C2738;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #5BA9F9;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #4A9AE8;
}

/* Message Bubbles */
.message-bubble {
    max-width: 70%;
    margin-bottom: 16px;
    padding: 16px 20px;
    border-radius: 16px;
    position: relative;
    word-wrap: break-word;
    user-select: text;
}

.message-bubble.user-message {
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
    color: #F9F9F9;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.message-bubble.ai-message {
    background: #1D1A2A;
    color: #F9F9F9;
    border: 2px solid #2C2738;
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

.message-bubble.system-message {
    background: #2C2738;
    color: #B2AFC5;
    margin: 0 auto;
    text-align: center;
    border-radius: 12px;
    max-width: 80%;
}

.message-content h3 {
    color: #F9F9F9;
    margin-bottom: 8px;
    font-size: 16px;
}

.message-content p {
    line-height: 1.5;
    margin-bottom: 8px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-timestamp {
    font-size: 12px;
    color: #B2AFC5;
    margin-top: 8px;
    opacity: 0.7;
}

/* Welcome Message */
.welcome-message {
    text-align: center;
    margin-bottom: 32px;
}

/* Chat Input */
.chat-input-container {
    background: #1D1A2A;
    border-top: 2px solid #2C2738;
    padding: 20px 24px;
}

.chat-input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

#chatInput {
    flex: 1;
    background: #2C2738;
    border: 2px solid #3D3A4A;
    border-radius: 12px;
    padding: 12px 16px;
    color: #F9F9F9;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    transition: border-color 0.2s ease;
    min-height: 44px;
    max-height: 120px;
}

#chatInput:focus {
    outline: none;
    border-color: #5BA9F9;
}

#chatInput::placeholder {
    color: #B2AFC5;
}

.send-btn {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
    border: none;
    border-radius: 12px;
    color: #F9F9F9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(91, 169, 249, 0.3);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.chat-input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

.character-count {
    font-size: 12px;
    color: #B2AFC5;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #B2AFC5;
}

.chat-disclaimer {
    text-align: center;
    font-size: 12px;
    color: #B2AFC5;
    margin-top: 4px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #5BA9F9;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: #4CAF50;
}

.status-indicator.disconnected {
    background: #FF5C5C;
}

.status-indicator.typing {
    background: #FFD84D;
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-indicator.active {
    display: flex;
}

.loading-content {
    background: #1D1A2A;
    border: 1px solid #2C2738;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top: 2px solid #5BA9F9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-content span {
    color: #F9F9F9;
    font-size: 14px;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 600px) {
    .chat-header {
        padding: 12px 16px;
    }
    
    .chat-messages {
        padding: 16px;
    }
    
    .chat-input-container {
        padding: 16px;
    }
    
    .message-bubble {
        max-width: 85%;
    }
    
    .logo-text h1 {
        font-size: 16px;
    }
}

/* Message Animation */
.message-bubble {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Typing Indicator */
.typing-container {
    opacity: 0.8;
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #B2AFC5;
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingBounce {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #5BA9F9;
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typingBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Additional animations for inline typing indicators */
@keyframes blink {
    0% { opacity: 0.1; }
    20% { opacity: 1; }
    100% { opacity: 0.1; }
}

.typing-indicator span {
    animation: blink 1.4s infinite both;
}

.typing-indicator span:nth-child(2) { 
    animation-delay: 0.2s; 
}

.typing-indicator span:nth-child(3) { 
    animation-delay: 0.4s; 
}

/* Hidden elements for JavaScript compatibility */
#conversation-list, #new-chat-btn {
    display: none;
}

/* Loading state improvements */
.loading-indicator {
    display: none;
}

.loading-indicator.active {
    display: flex;
}